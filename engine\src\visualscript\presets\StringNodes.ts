/**
 * 视觉脚本字符串节点
 * 提供字符串操作相关的节点
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 字符串连接节点
 * 连接多个字符串
 */
export class StringConcatNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加第一个字符串输入
    this.addInput({
      name: 'str1',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '第一个字符串',
      defaultValue: ''
    });

    // 添加第二个字符串输入
    this.addInput({
      name: 'str2',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '第二个字符串',
      defaultValue: ''
    });

    // 添加分隔符输入
    this.addInput({
      name: 'separator',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '分隔符',
      defaultValue: ''
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '连接后的字符串'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const str1 = String(this.getInputValue('str1') || '');
    const str2 = String(this.getInputValue('str2') || '');
    const separator = String(this.getInputValue('separator') || '');

    // 连接字符串
    const result = str1 + separator + str2;

    // 设置输出值
    this.setOutputValue('result', result);

    return result;
  }
}

/**
 * 字符串分割节点
 * 按指定分隔符分割字符串
 */
export class StringSplitNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加字符串输入
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '要分割的字符串',
      defaultValue: ''
    });

    // 添加分隔符输入
    this.addInput({
      name: 'separator',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '分隔符',
      defaultValue: ','
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '分割后的字符串数组'
    });

    // 添加长度输出
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '分割后数组的长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const string = String(this.getInputValue('string') || '');
    const separator = String(this.getInputValue('separator') || ',');

    // 分割字符串
    const result = string.split(separator);

    // 设置输出值
    this.setOutputValue('result', result);
    this.setOutputValue('length', result.length);

    return result;
  }
}

/**
 * 注册字符串节点
 * @param registry 节点注册表
 */
export function registerStringNodes(registry: NodeRegistry): void {
  // 注册字符串连接节点
  registry.registerNodeType({
    type: 'string/concat',
    category: NodeCategory.STRING,
    constructor: StringConcatNode,
    label: '字符串连接',
    description: '连接多个字符串',
    icon: 'link',
    color: '#52C41A',
    tags: ['string', 'concat', 'join']
  });

  // 注册字符串分割节点
  registry.registerNodeType({
    type: 'string/split',
    category: NodeCategory.STRING,
    constructor: StringSplitNode,
    label: '字符串分割',
    description: '按指定分隔符分割字符串',
    icon: 'scissor',
    color: '#52C41A',
    tags: ['string', 'split', 'array']
  });
}
