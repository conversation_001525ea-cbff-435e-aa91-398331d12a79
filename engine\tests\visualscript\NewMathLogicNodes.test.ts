/**
 * 新数学和逻辑节点单元测试
 * 测试新实现的数学运算和逻辑比较节点
 */
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { NodeRegistry } from '../../src/visualscript/nodes/NodeRegistry';
import { 
  SubtractNode, 
  MultiplyNode, 
  DivideNode, 
  ModuloNode, 
  PowerNode, 
  SquareRootNode,
  AbsoluteValueNode,
  MinNode,
  MaxNode,
  ClampNode
} from '../../src/visualscript/presets/MathNodes';
import { ComparisonNode } from '../../src/visualscript/presets/LogicNodes';
import { NodeCategory } from '../../src/visualscript/nodes/Node';

describe('新数学节点测试', () => {
  let registry: NodeRegistry;

  beforeEach(() => {
    registry = new NodeRegistry();
  });

  describe('减法节点', () => {
    it('应该正确计算两个数的差', () => {
      const node = new SubtractNode({
        id: 'subtract-test',
        type: 'math/basic/subtract',
        metadata: {},
        graph: null,
        context: null
      });

      // 模拟输入值
      node.getInputValue = (name: string) => {
        if (name === 'a') return 10;
        if (name === 'b') return 3;
        return 0;
      };

      // 模拟输出设置
      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      // 模拟流程触发
      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(7);
      expect(outputValue!).toBe(7);
    });
  });

  describe('乘法节点', () => {
    it('应该正确计算两个数的积', () => {
      const node = new MultiplyNode({
        id: 'multiply-test',
        type: 'math/basic/multiply',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'a') return 4;
        if (name === 'b') return 5;
        return 0;
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(20);
      expect(outputValue!).toBe(20);
    });
  });

  describe('除法节点', () => {
    it('应该正确计算两个数的商', () => {
      const node = new DivideNode({
        id: 'divide-test',
        type: 'math/basic/divide',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'a') return 15;
        if (name === 'b') return 3;
        return 0;
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(5);
      expect(outputValue!).toBe(5);
    });

    it('应该处理除零错误', () => {
      const node = new DivideNode({
        id: 'divide-test',
        type: 'math/basic/divide',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'a') return 10;
        if (name === 'b') return 0;
        return 0;
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(isNaN(result)).toBe(true);
    });
  });

  describe('取模节点', () => {
    it('应该正确计算两个数的模', () => {
      const node = new ModuloNode({
        id: 'modulo-test',
        type: 'math/basic/modulo',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'a') return 17;
        if (name === 'b') return 5;
        return 0;
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(2);
      expect(outputValue!).toBe(2);
    });
  });

  describe('幂运算节点', () => {
    it('应该正确计算幂运算', () => {
      const node = new PowerNode({
        id: 'power-test',
        type: 'math/advanced/power',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'base') return 2;
        if (name === 'exponent') return 3;
        return 0;
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(8);
      expect(outputValue!).toBe(8);
    });
  });

  describe('平方根节点', () => {
    it('应该正确计算平方根', () => {
      const node = new SquareRootNode({
        id: 'sqrt-test',
        type: 'math/advanced/sqrt',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'value') return 16;
        return 0;
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(4);
      expect(outputValue!).toBe(4);
    });

    it('应该处理负数输入', () => {
      const node = new SquareRootNode({
        id: 'sqrt-test',
        type: 'math/advanced/sqrt',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'value') return -4;
        return 0;
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(isNaN(result)).toBe(true);
    });
  });

  describe('绝对值节点', () => {
    it('应该正确计算正数的绝对值', () => {
      const node = new AbsoluteValueNode({
        id: 'abs-test',
        type: 'math/advanced/abs',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'value') return 5;
        return 0;
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(5);
      expect(outputValue!).toBe(5);
    });

    it('应该正确计算负数的绝对值', () => {
      const node = new AbsoluteValueNode({
        id: 'abs-test',
        type: 'math/advanced/abs',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'value') return -7;
        return 0;
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(7);
      expect(outputValue!).toBe(7);
    });
  });

  describe('最小值节点', () => {
    it('应该正确计算两个数的最小值', () => {
      const node = new MinNode({
        id: 'min-test',
        type: 'math/advanced/min',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'a') return 8;
        if (name === 'b') return 3;
        return 0;
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(3);
      expect(outputValue!).toBe(3);
    });
  });

  describe('最大值节点', () => {
    it('应该正确计算两个数的最大值', () => {
      const node = new MaxNode({
        id: 'max-test',
        type: 'math/advanced/max',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'a') return 8;
        if (name === 'b') return 12;
        return 0;
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(12);
      expect(outputValue!).toBe(12);
    });
  });

  describe('限制范围节点', () => {
    it('应该正确限制数值在范围内', () => {
      const node = new ClampNode({
        id: 'clamp-test',
        type: 'math/advanced/clamp',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'value') return 15;
        if (name === 'min') return 0;
        if (name === 'max') return 10;
        return 0;
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(10);
      expect(outputValue!).toBe(10);
    });

    it('应该正确处理在范围内的值', () => {
      const node = new ClampNode({
        id: 'clamp-test',
        type: 'math/advanced/clamp',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'value') return 5;
        if (name === 'min') return 0;
        if (name === 'max') return 10;
        return 0;
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(5);
      expect(outputValue!).toBe(5);
    });

    it('应该正确处理小于最小值的情况', () => {
      const node = new ClampNode({
        id: 'clamp-test',
        type: 'math/advanced/clamp',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'value') return -5;
        if (name === 'min') return 0;
        if (name === 'max') return 10;
        return 0;
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(0);
      expect(outputValue!).toBe(0);
    });
  });
});

describe('逻辑比较节点测试', () => {
  describe('相等比较节点', () => {
    it('应该正确比较两个相等的值', () => {
      const node = new ComparisonNode({
        id: 'equal-test',
        type: 'logic/comparison/equal',
        metadata: { operator: 'equal' },
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'a') return 5;
        if (name === 'b') return 5;
        return 0;
      };

      let outputValue: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(true);
      expect(outputValue!).toBe(true);
    });

    it('应该正确比较两个不相等的值', () => {
      const node = new ComparisonNode({
        id: 'equal-test',
        type: 'logic/comparison/equal',
        metadata: { operator: 'equal' },
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'a') return 5;
        if (name === 'b') return 3;
        return 0;
      };

      let outputValue: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(false);
      expect(outputValue!).toBe(false);
    });
  });

  describe('不相等比较节点', () => {
    it('应该正确比较两个不相等的值', () => {
      const node = new ComparisonNode({
        id: 'notequal-test',
        type: 'logic/comparison/notEqual',
        metadata: { operator: 'notEqual' },
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'a') return 5;
        if (name === 'b') return 3;
        return 0;
      };

      let outputValue: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(true);
      expect(outputValue!).toBe(true);
    });
  });

  describe('大于比较节点', () => {
    it('应该正确比较大于关系', () => {
      const node = new ComparisonNode({
        id: 'greater-test',
        type: 'logic/comparison/greater',
        metadata: { operator: 'greater' },
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'a') return 8;
        if (name === 'b') return 3;
        return 0;
      };

      let outputValue: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(true);
      expect(outputValue!).toBe(true);
    });
  });

  describe('大于等于比较节点', () => {
    it('应该正确比较大于等于关系', () => {
      const node = new ComparisonNode({
        id: 'greaterequal-test',
        type: 'logic/comparison/greaterEqual',
        metadata: { operator: 'greaterEqual' },
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'a') return 5;
        if (name === 'b') return 5;
        return 0;
      };

      let outputValue: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(true);
      expect(outputValue!).toBe(true);
    });
  });

  describe('小于比较节点', () => {
    it('应该正确比较小于关系', () => {
      const node = new ComparisonNode({
        id: 'less-test',
        type: 'logic/comparison/less',
        metadata: { operator: 'less' },
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'a') return 3;
        if (name === 'b') return 8;
        return 0;
      };

      let outputValue: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(true);
      expect(outputValue!).toBe(true);
    });
  });

  describe('小于等于比较节点', () => {
    it('应该正确比较小于等于关系', () => {
      const node = new ComparisonNode({
        id: 'lessequal-test',
        type: 'logic/comparison/lessEqual',
        metadata: { operator: 'lessEqual' },
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'a') return 5;
        if (name === 'b') return 5;
        return 0;
      };

      let outputValue: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      node.triggerFlow = () => {};

      const result = node.execute();
      expect(result).toBe(true);
      expect(outputValue!).toBe(true);
    });
  });
});
