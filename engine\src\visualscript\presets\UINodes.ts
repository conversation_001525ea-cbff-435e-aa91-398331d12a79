/**
 * 视觉脚本UI节点
 * 提供UI交互相关的节点
 */
import { EventNode } from '../nodes/EventNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 按钮点击事件节点
 * 监听按钮点击事件
 */
export class ButtonClickNode extends EventNode {
  private element: HTMLElement | null = null;
  private clickHandler: ((event: Event) => void) | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加按钮ID输入
    this.addInput({
      name: 'buttonId',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '按钮元素ID',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '点击时触发'
    });

    // 添加事件信息输出
    this.addOutput({
      name: 'event',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '点击事件信息'
    });

    // 添加时间戳输出
    this.addOutput({
      name: 'timestamp',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '点击时间戳'
    });
  }

  /**
   * 启动事件监听
   */
  public start(): void {
    const buttonId = String(this.getInputValue('buttonId') || '');
    
    if (buttonId) {
      this.element = document.getElementById(buttonId);
      
      if (this.element) {
        this.clickHandler = (event: Event) => {
          // 设置输出值
          this.setOutputValue('event', {
            type: event.type,
            target: event.target,
            currentTarget: event.currentTarget
          });
          this.setOutputValue('timestamp', Date.now());

          // 触发流程
          this.triggerFlow('flow');
        };

        this.element.addEventListener('click', this.clickHandler);
      }
    }
  }

  /**
   * 停止事件监听
   */
  public stop(): void {
    if (this.element && this.clickHandler) {
      this.element.removeEventListener('click', this.clickHandler);
      this.element = null;
      this.clickHandler = null;
    }
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    this.stop();
    super.dispose();
  }
}

/**
 * 输入框变化事件节点
 * 监听输入框内容变化事件
 */
export class InputChangeNode extends EventNode {
  private element: HTMLInputElement | null = null;
  private changeHandler: ((event: Event) => void) | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入框ID输入
    this.addInput({
      name: 'inputId',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '输入框元素ID',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '内容变化时触发'
    });

    // 添加输入值输出
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '输入框当前值'
    });

    // 添加事件信息输出
    this.addOutput({
      name: 'event',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '变化事件信息'
    });
  }

  /**
   * 启动事件监听
   */
  public start(): void {
    const inputId = String(this.getInputValue('inputId') || '');
    
    if (inputId) {
      this.element = document.getElementById(inputId) as HTMLInputElement;
      
      if (this.element) {
        this.changeHandler = (event: Event) => {
          const target = event.target as HTMLInputElement;
          
          // 设置输出值
          this.setOutputValue('value', target.value);
          this.setOutputValue('event', {
            type: event.type,
            target: event.target,
            currentTarget: event.currentTarget
          });

          // 触发流程
          this.triggerFlow('flow');
        };

        this.element.addEventListener('input', this.changeHandler);
        this.element.addEventListener('change', this.changeHandler);
      }
    }
  }

  /**
   * 停止事件监听
   */
  public stop(): void {
    if (this.element && this.changeHandler) {
      this.element.removeEventListener('input', this.changeHandler);
      this.element.removeEventListener('change', this.changeHandler);
      this.element = null;
      this.changeHandler = null;
    }
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    this.stop();
    super.dispose();
  }
}

/**
 * 滑块值变化事件节点
 * 监听滑块值变化事件
 */
export class SliderValueChangeNode extends EventNode {
  private element: HTMLInputElement | null = null;
  private changeHandler: ((event: Event) => void) | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加滑块ID输入
    this.addInput({
      name: 'sliderId',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '滑块元素ID',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '值变化时触发'
    });

    // 添加数值输出
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '滑块当前值'
    });

    // 添加百分比输出
    this.addOutput({
      name: 'percentage',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '滑块百分比值'
    });
  }

  /**
   * 启动事件监听
   */
  public start(): void {
    const sliderId = String(this.getInputValue('sliderId') || '');
    
    if (sliderId) {
      this.element = document.getElementById(sliderId) as HTMLInputElement;
      
      if (this.element && this.element.type === 'range') {
        this.changeHandler = (event: Event) => {
          const target = event.target as HTMLInputElement;
          const value = parseFloat(target.value);
          const min = parseFloat(target.min) || 0;
          const max = parseFloat(target.max) || 100;
          const percentage = ((value - min) / (max - min)) * 100;
          
          // 设置输出值
          this.setOutputValue('value', value);
          this.setOutputValue('percentage', percentage);

          // 触发流程
          this.triggerFlow('flow');
        };

        this.element.addEventListener('input', this.changeHandler);
        this.element.addEventListener('change', this.changeHandler);
      }
    }
  }

  /**
   * 停止事件监听
   */
  public stop(): void {
    if (this.element && this.changeHandler) {
      this.element.removeEventListener('input', this.changeHandler);
      this.element.removeEventListener('change', this.changeHandler);
      this.element = null;
      this.changeHandler = null;
    }
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    this.stop();
    super.dispose();
  }
}

/**
 * 注册UI节点
 * @param registry 节点注册表
 */
export function registerUINodes(registry: NodeRegistry): void {
  // 注册按钮点击事件节点
  registry.registerNodeType({
    type: 'ui/button/onClick',
    category: NodeCategory.UI,
    constructor: ButtonClickNode,
    label: '按钮点击事件',
    description: '监听按钮点击事件',
    icon: 'click',
    color: '#EB2F96',
    tags: ['ui', 'button', 'click', 'event']
  });

  // 注册输入框变化事件节点
  registry.registerNodeType({
    type: 'ui/input/onChange',
    category: NodeCategory.UI,
    constructor: InputChangeNode,
    label: '输入框变化事件',
    description: '监听输入框内容变化事件',
    icon: 'edit',
    color: '#EB2F96',
    tags: ['ui', 'input', 'change', 'event']
  });

  // 注册滑块值变化事件节点
  registry.registerNodeType({
    type: 'ui/slider/onValueChange',
    category: NodeCategory.UI,
    constructor: SliderValueChangeNode,
    label: '滑块值变化事件',
    description: '监听滑块值变化事件',
    icon: 'slider',
    color: '#EB2F96',
    tags: ['ui', 'slider', 'range', 'change', 'event']
  });
}
