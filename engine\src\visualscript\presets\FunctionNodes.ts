/**
 * 视觉脚本函数节点
 * 提供函数调用相关的节点
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 函数调用节点
 * 调用指定的函数
 */
export class FunctionCallNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加函数名输入
    this.addInput({
      name: 'functionName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '函数名',
      defaultValue: ''
    });

    // 添加参数输入
    this.addInput({
      name: 'args',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '函数参数数组',
      defaultValue: []
    });

    // 添加上下文输入
    this.addInput({
      name: 'context',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '函数执行上下文',
      defaultValue: null
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加返回值输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '函数返回值'
    });

    // 添加成功状态输出
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '调用是否成功'
    });

    // 添加错误信息输出
    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const functionName = String(this.getInputValue('functionName') || '');
    const args = this.getInputValue('args') || [];
    const context = this.getInputValue('context');

    let result = null;
    let success = false;
    let error = '';

    try {
      if (functionName) {
        // 尝试从全局作用域获取函数
        const func = (window as any)[functionName] || (global as any)?.[functionName];
        
        if (typeof func === 'function') {
          // 调用函数
          result = context ? func.apply(context, args) : func(...args);
          success = true;
        } else {
          error = `函数 "${functionName}" 不存在或不是一个函数`;
        }
      } else {
        error = '函数名不能为空';
      }
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
      success = false;
    }

    // 设置输出值
    this.setOutputValue('result', result);
    this.setOutputValue('success', success);
    this.setOutputValue('error', error);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 函数返回节点
 * 从函数中返回值
 */
export class FunctionReturnNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加返回值输入
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '返回值',
      defaultValue: null
    });

    // 添加返回值输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '返回值'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const value = this.getInputValue('value');

    // 设置输出值
    this.setOutputValue('result', value);

    // 注意：这里不触发流程，因为return应该终止执行
    return value;
  }
}

/**
 * 注册函数节点
 * @param registry 节点注册表
 */
export function registerFunctionNodes(registry: NodeRegistry): void {
  // 注册函数调用节点
  registry.registerNodeType({
    type: 'function/call',
    category: NodeCategory.FUNCTION,
    constructor: FunctionCallNode,
    label: '调用函数',
    description: '调用指定的函数',
    icon: 'function',
    color: '#13C2C2',
    tags: ['function', 'call', 'invoke']
  });

  // 注册函数返回节点
  registry.registerNodeType({
    type: 'function/return',
    category: NodeCategory.FUNCTION,
    constructor: FunctionReturnNode,
    label: '返回值',
    description: '从函数中返回值',
    icon: 'rollback',
    color: '#13C2C2',
    tags: ['function', 'return', 'exit']
  });
}
