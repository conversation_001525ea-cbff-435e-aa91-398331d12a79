/**
 * 视觉脚本预设节点
 * 提供各种预设节点的注册
 */
import { NodeRegistry } from '../nodes/NodeRegistry';
import { registerMathNodes } from './MathNodes';
import { registerLogicNodes } from './LogicNodes';
import { registerEntityNodes } from './EntityNodes';
import { registerStringNodes } from './StringNodes';
import { registerArrayNodes } from './ArrayNodes';
import { registerObjectNodes } from './ObjectNodes';
import { registerVariableNodes } from './VariableNodes';
import { registerFunctionNodes } from './FunctionNodes';
import { registerUINodes } from './UINodes';

/**
 * 注册所有预设节点
 * @param registry 节点注册表
 */
export function registerAllPresetNodes(registry: NodeRegistry): void {
  // 注册数学节点
  registerMathNodes(registry);

  // 注册逻辑节点
  registerLogicNodes(registry);

  // 注册实体节点
  registerEntityNodes(registry);

  // 注册字符串节点
  registerStringNodes(registry);

  // 注册数组节点
  registerArrayNodes(registry);

  // 注册对象节点
  registerObjectNodes(registry);

  // 注册变量节点
  registerVariableNodes(registry);

  // 注册函数节点
  registerFunctionNodes(registry);

  // 注册UI节点
  registerUINodes(registry);
}

export * from './MathNodes';
export * from './LogicNodes';
export * from './EntityNodes';
export * from './StringNodes';
export * from './ArrayNodes';
export * from './ObjectNodes';
export * from './VariableNodes';
export * from './FunctionNodes';
export * from './UINodes';
